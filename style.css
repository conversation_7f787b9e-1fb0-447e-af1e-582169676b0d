:root {
  --bg-dark: #1a1a2e;
  --bg-light: #16213e;
  --text-primary: #e0e0e0;
  --text-secondary: #ffffff;
  --accent: #00f5c3;
  --accent-dark: #00d1a7;
}

[data-theme="light"] {
  --bg-dark: #f0f2f5;
  --bg-light: #ffffff;
  --text-primary: #333333;
  --text-secondary: #555555;
  --accent: #007bff;
  --accent-dark: #0056b3;
}

/* Global resets and typography */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html {
  scroll-behavior: smooth;
}

.skip-to-content {
  position: absolute;
  left: -10000px;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

.skip-to-content:focus {
  position: static;
  width: auto;
  height: auto;
}

.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background-color: var(--accent);
  color: var(--bg-dark);
  padding: 1rem;
  border-radius: 50%;
  display: none;
  z-index: 1000;
}

.back-to-top.visible {
  display: block;
}

body {
  font-family: "Segoe UI", Tahoma, sans-serif;
  color: var(--text-primary);
  line-height: 1.6;
  background-color: var(--bg-dark);
}

a {
  color: var(--accent);
  text-decoration: none;
}

img {
  max-width: 100%;
  display: block;
}

/* Light background fixed header */
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: var(--bg-light);
  z-index: 1000;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

/* Full-width nav */
nav {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  padding: 1rem;
  position: relative;
}

/* Theme toggle button */
.theme-toggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: 2px solid var(--accent);
  color: var(--accent);
  padding: 0.5rem;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.theme-toggle:hover {
  background-color: var(--accent);
  color: var(--bg-dark);
  transform: scale(1.1);
}

/* Hide hamburger on desktop */
.hamburger {
  display: none;
}

/* Nav links */
nav a {
  color: var(--text-primary);
  font-weight: bold;
  text-transform: capitalize;
  font-size: 0.9em;
  padding: 0.5em 1em;
  border-radius: 6px;
  transition: all 0.3s ease;
}

/* Hover effect */
nav a:hover {
  background-color: var(--accent);
  color: var(--bg-light);
  opacity: 0.9;
}

/* === HERO SECTION FIXES AND ENHANCEMENTS === */
.hero {
  position: relative;
  height: calc(100vh - 60px); /* reduce height by header height */
  padding-top: 60px; /* space for fixed header */
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 40px;
  text-align: left;
  overflow: hidden;
  color: var(--text-secondary);
  z-index: 1;
}

/* Background with dark overlay gradient */
.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.2)),
    url("https://i.ibb.co/CpVhJTwm/final.webp") center/cover no-repeat;
  z-index: 0;
}

/* Hero text */
.hero-content {
  position: relative;
  z-index: 2;
  max-width: 600px;
  animation: fadeUp 1s ease forwards;
  opacity: 0;
}

.hero-content h1 {
  font-size: clamp(2.5rem, 6vw, 4rem);
  margin-bottom: 0.5em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-content p {
  font-size: clamp(1rem, 2.5vw, 1.4rem);
  margin-bottom: 1em;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.cta-button {
  display: inline-block;
  padding: 0.75em 1.5em;
  background: var(--accent);
  color: var(--bg-dark);
  border-radius: 4px;
  font-weight: 600;
  transition: background 0.3s, transform 0.3s;
}

.cta-button:hover {
  background: var(--accent-dark);
  transform: scale(1.05);
}

/* Hero social icons */
.social-icons {
  position: absolute;
  top: 30px;
  right: 30px;
  z-index: 2;
  display: flex;
  gap: 15px;
}

.social-icons img {
  width: 28px;
  height: 28px;
  filter: brightness(0) invert(1);
  transition: transform 0.3s;
}

.social-icons img:hover {
  transform: scale(1.2);
}

/* Scroll-down indicator */
.scroll-down {
  cursor: pointer;
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 1.5rem;
  color: var(--text-secondary);
  z-index: 2;
  animation: bounce 2s infinite;
}

/* Fade & Bounce Animations */
.fade-in {
  animation: fadeInAnimation 1.5s ease forwards;
  opacity: 0;
}

@keyframes fadeInAnimation {
  to {
    opacity: 1;
  }
}

@keyframes fadeUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(10px);
  }
}

/* Section layout */
main {
  padding-top: 60px;
}
section {
  padding: 60px 20px;
  max-width: 1000px;
  margin: 0 auto;
}
h2 {
  margin-bottom: 0.5em;
}

/* About Section */
.about-text,
.about-image {
  flex: 1 1 100%;
  max-width: 100%;
}

#about {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
#about .about-text {
  flex: 1 1 300px;
  padding: 20px;
}
#about .about-image {
  flex: 1 1 300px;
  padding: 20px;
}
#about img {
  border-radius: 4px;
}

/* === Light Themed Scrolling Skills Section === */
.skills-marquee {
  background: var(--bg-dark);
  padding: 60px 0;
  text-align: center;
  overflow: hidden;
  position: relative;
}

.skills-title {
  font-size: 2.6rem;
  font-weight: 700;
  color: var(--text-secondary);
  margin-bottom: 40px;
}

.marquee-wrapper {
  overflow: hidden;
  position: relative;
}

.marquee-track {
  display: flex;
  gap: 60px;
  animation: scrollLinear 25s linear infinite;
  width: fit-content;
}

.skill-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--text-primary);
  min-width: 120px;
  transition: transform 0.3s ease;
}

.skill-item img {
  width: 60px;
  height: 60px;
  margin-bottom: 10px;
  filter: drop-shadow(0 0 6px rgba(0, 0, 0, 0.1));
}

.skill-item span {
  font-size: 14px;
  font-weight: 500;
}

.skill-item:hover {
  transform: scale(1.1);
}

/* Infinite scrolling animation */
@keyframes scrollLinear {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Scrolling Animation */
@keyframes scrollLeft {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Stagger animation delay */
.skill-item:nth-child(1) {
  animation-delay: 0.1s;
}
.skill-item:nth-child(2) {
  animation-delay: 0.2s;
}
.skill-item:nth-child(3) {
  animation-delay: 0.3s;
}
.skill-item:nth-child(4) {
  animation-delay: 0.4s;
}
.skill-item:nth-child(5) {
  animation-delay: 0.5s;
}
.skill-item:nth-child(6) {
  animation-delay: 0.6s;
}

/* Keyframes for fade-up animation */
@keyframes fadeUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.timeline li {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeUp 0.8s ease forwards;
}

.timeline li:nth-child(1) {
  animation-delay: 0.2s;
}
.timeline li:nth-child(2) {
  animation-delay: 0.4s;
}
/* Add more if needed */

#philosophy {
  background: var(--bg-light);
  padding: 60px 30px;
  font-size: 1.2rem;
  font-style: italic;
  border-left: 5px solid var(--accent);
  max-width: 800px;
  margin: 60px auto;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  opacity: 0;
  transform: translateY(30px);
  animation: fadeUp 1s ease forwards;
}

/* Projects */
#projects {
  background: var(--bg-light);
  padding: 50px 20px;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-primary);
}

#projects h2 {
  text-align: center;
  font-size: 2.6rem;
  margin-bottom: 40px;
  color: var(--text-secondary);
  font-weight: 700;
  letter-spacing: 1.2px;
  text-transform: none;
}

#projects .project-list {
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
  justify-content: center;
}

#projects .project-item {
  flex: 1 1 280px;
  background: var(--bg-dark);
  border-radius: 12px;
  border: 1px solid var(--accent);
  overflow: hidden;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
}

#projects .project-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

#projects .project-item img {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-bottom: 4px solid var(--accent);
  transition: transform 0.3s ease;
}

#projects .project-item:hover img {
  transform: scale(1.05);
}

#projects .project-item h3 {
  font-size: 1.3rem;
  margin: 16px 16px 8px;
  color: var(--text-secondary);
  font-weight: 700;
}

#projects .project-item p {
  flex-grow: 1;
  font-size: 1rem;
  margin: 0 16px 20px;
  color: var(--text-primary);
  line-height: 1.5;
}

/* Timelines (Experience, Education) */
.timeline {
  list-style: none;
  border-left: 2px solid var(--accent);
  margin-left: 10px;
  padding: 10px 20px;
}
.timeline li {
  position: relative;
  margin-bottom: 20px;
  padding-left: 20px;
}
.timeline li::before {
  content: "";
  position: absolute;
  left: -10px;
  top: 0;
  width: 20px;
  height: 20px;
  background: var(--accent);
  border-radius: 50%;
}
.timeline li {
  position: relative;
  margin-bottom: 20px;
  padding-left: 20px;
  cursor: pointer;
  transition: box-shadow 0.3s ease, transform 0.3s ease,
    background-color 0.3s ease;
}

.timeline li:hover {
  background-color: var(--accent);
  color: var(--bg-light);
  opacity: 0.9;
  transform: translateY(-3px);
  border-radius: 6px;
  z-index: 10;
}

/* Hover effect for experience section */
#experience .timeline li:hover {
  background-color: var(--accent);
  color: var(--bg-light);
  opacity: 0.9;
  transform: translateY(-3px);
  z-index: 10;
}

#testimonials {
  background: var(--bg-dark); /* soft lavender background */
  padding: 60px 20px;
  font-family: "Segoe UI", Tahoma, sans-serif;
  text-align: center;
  color: var(--text-primary); /* default text color */
}

#testimonials h2 {
  font-size: 2.6rem;
  color: var(--text-secondary); /* black color for the heading */
}

.testimonial-item {
  background: var(--bg-light);
  padding: 40px 20px 30px; /* Top padding increased to 40px */
  border-radius: 12px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  color: var(--text-primary);
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

#testimonials .testimonial-item:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

#testimonials .testimonial-item p:first-of-type {
  color: var(--text-secondary); /* black for testimonial quotes */
  font-style: italic;
  font-size: 1rem;
  margin-bottom: 10px;
  line-height: 1.6;
}

#testimonials .testimonial-item em {
  color: var(--accent); /* accent color for author name */
  font-style: normal;
  font-weight: bold;
  font-size: 0.95rem;
}

#testimonials .testimonial-item::before {
  content: "❝";
  font-size: 4rem;
  color: var(--text-primary);
  position: absolute;
  top: 10px;
  left: 20px;
  z-index: 0;
  opacity: 0.3;
}
.testimonial-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 15px;
  border: 2px solid var(--accent);
  display: block;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* Contact Form */
form {
  display: flex;
  flex-direction: column;
}
form input,
form textarea {
  font-size: 1rem;
  padding: 12px;
  border: 1px solid var(--bg-light);
  border-radius: 6px;
  transition: border-color 0.3s, box-shadow 0.3s;
  margin-bottom: 10px;
  background-color: var(--bg-light);
  color: var(--text-primary);
}

form input:focus,
form textarea:focus {
  border-color: var(--accent);
  box-shadow: 0 0 5px var(--accent);
  outline: none;
}
form button {
  padding: 10px;
  border: none;
  background: var(--accent);
  color: var(--bg-light);
  border-radius: 4px;
  cursor: pointer;
}
form button:hover {
  background: var(--accent-dark);
}

/* Fade-up animation */
.fade-up {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}
.fade-up.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Social Media Icons on Hero Top Right */
.hero {
  position: relative;
}

.social-icons {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 15px;
  z-index: 1000;
}

.social-icons a {
  display: inline-block;
  width: 36px;
  height: 36px;
  transition: transform 0.3s ease, filter 0.3s ease;
}

.social-icons img {
  width: 28px;
  height: 28px;
  filter: brightness(0) invert(1);
  opacity: 0.7; /* Add transparency */
  transition: transform 0.3s, opacity 0.3s;
}

.social-icons img:hover {
  transform: scale(1.2);
  opacity: 1; /* Full opacity on hover */
}

/* ✨ Soft Background Colors Per Section */
#about {
  background: var(--bg-light);
}

#skills {
  background: var(--bg-dark);
}

#projects {
  background: var(--bg-light);
}

/* Timeline style consistent for education & experience */
.timeline {
  list-style: none;
  border-left: 2px solid var(--accent); /* accent vertical line */
  margin-left: 10px;
  padding: 10px 20px;
}

.timeline li {
  position: relative;
  margin-bottom: 20px;
  padding-left: 20px;
}

.timeline li::before {
  content: "";
  position: absolute;
  left: -10px;
  top: 10px;
  width: 20px;
  height: 20px;
  background: var(--accent);
  border-radius: 50%;
}

/* Experience Section Styling */
#experience {
  background: var(--bg-dark);
  padding: 60px 20px;
  font-family: "Segoe UI", Tahoma, sans-serif;
  color: var(--text-primary);
  border-radius: 12px;
  max-width: 900px;
  margin: 0 auto 60px auto;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
}

#experience:hover {
  box-shadow: 0 25px 60px var(--accent);
  transform: translateY(-6px);
}

#experience h2 {
  font-size: 2.6rem;
  color: var(--text-secondary);
  font-weight: 700;
  margin-bottom: 30px;
  text-align: center;
}

#experience .timeline {
  list-style: none;
  padding-left: 0;
}

#experience li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25px;
  padding-left: 10px;
  position: relative;
}

#experience li i.fas.fa-briefcase {
  color: var(--accent);
  font-size: 22px;
  margin-right: 15px;
  flex-shrink: 0;
  margin-top: 4px;
}

#experience li div strong {
  font-size: 1.2rem;
  color: var(--text-secondary);
  font-weight: 600;
}

#experience li div small {
  font-size: 0.95rem;
  color: var(--text-primary);
  display: block;
  margin-top: 6px;
  line-height: 1.4;
}

/* Education Section Styling */
#education {
  padding: 60px 20px;
  font-family: "Segoe UI", Tahoma, sans-serif;
  color: var(--text-primary);
  border-radius: 12px;
  max-width: 1000px;
  margin: 0 auto 60px auto;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
  background: var(--bg-light);
}

#education:hover {
  box-shadow: 0 25px 60px var(--accent);
  transform: translateY(-6px);
}

#education h2 {
  font-size: 2.6rem;
  color: var(--text-secondary);
  font-weight: 700;
  margin-bottom: 30px;
  text-align: center;
}

#education .timeline {
  list-style: none;
  padding-left: 0;
}

#education li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25px;
  padding-left: 10px;
  position: relative;
}

#education li i.fas.fa-graduation-cap {
  color: var(--accent);
  font-size: 22px;
  margin-right: 15px;
  flex-shrink: 0;
  margin-top: 4px;
}

#education li div strong {
  font-size: 1.2rem;
  color: var(--text-secondary);
  font-weight: 600;
}

#education li div small {
  font-size: 0.95rem;
  color: var(--text-primary);
  display: block;
  margin-top: 6px;
  line-height: 1.4;
}

#testimonials {
  background: var(--bg-dark);
}

#philosophy {
  background: var(--bg-light);
}

#contact {
  background: var(--bg-dark);
}

#philosophy {
  background: var(--bg-light);
  padding: 50px 40px;
  border-radius: 16px;
  max-width: 1000px;
  margin: 50px auto;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.02);
  font-family: "Segoe UI", Tahoma, sans-serif;
  color: var(--text-primary);
  text-align: center;
  position: relative;
  transition: box-shadow 0.4s ease, background-color 0.4s ease;
}

#philosophy:hover {
  background-color: var(--bg-dark);
  box-shadow: 0 0 12px 5px var(--accent);
}

#philosophy h2 {
  color: var(--text-secondary);
  font-size: 2.6rem;
  margin-bottom: 25px;
  letter-spacing: 1.2px;
  font-weight: 700;
}

/* Make main page sections a responsive width and centered */
#hero,
#about,
#expertise,
#projects,
#experience,
#education,
#testimonials,
#philosophy,
#contact,
#last-page {
  width: 90%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px 10px;
  box-sizing: border-box;
}

#last-page .contact-container {
  display: flex;
  justify-content: space-around;
  gap: 25px;
  flex-wrap: wrap;
}

#last-page .contact-box {
  background: var(--bg-light);
  border-radius: 12px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
  padding: 20px 30px;
  max-width: 280px;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 18px;
  cursor: default;
  transition: box-shadow 0.3s ease, transform 0.3s ease,
    background-color 0.3s ease;
}

#last-page .contact-box i {
  color: var(--accent);
  font-size: 28px;
  flex-shrink: 0;
}

#last-page .contact-info strong {
  display: block;
  font-size: 1.1rem;
  color: var(--text-secondary);
  margin-bottom: 6px;
}

#last-page .contact-info a,
#last-page .contact-info span {
  font-size: 1rem;
  color: var(--text-primary);
  text-decoration: none;
}

#last-page .contact-box:hover {
  box-shadow: 0 20px 40px var(--accent);
  background-color: var(--bg-dark);
  transform: translateY(-5px);
}
.social-link i {
  font-size: 24px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.social-link {
  padding: 12px;
  border-radius: 50%;
  background-color: var(--bg-light);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease,
    background-color 0.3s ease;
}

/* Hover Effects */
.social-link:hover {
  transform: scale(1.15);
  box-shadow: 0 10px 20px var(--accent);
  background-color: var(--bg-dark);
}
.footer-social-icons {
  display: flex;
  justify-content: center;
  gap: 25px;
  flex-wrap: wrap;
  margin-top: 25px;
}

/* Brand Icon Colors */
.social-link.instagram i {
  color: #e4405f;
}
.social-link.facebook i {
  color: #3b5998;
}
.social-link.linkedin i {
  color: #0077b5;
}
.download-cv-btn {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 24px;
  margin-top: 25px;
  background-color: var(--accent);
  color: var(--bg-light);
  font-weight: 600;
  font-size: 1rem;
  border-radius: 8px;
  text-decoration: none;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  transition: background-color 0.3s ease, transform 0.3s ease,
    box-shadow 0.3s ease;
}

.download-cv-btn i {
  font-size: 1.1rem;
}

.download-cv-btn:hover {
  background-color: var(--accent-dark);
  transform: translateY(-3px);
  box-shadow: 0 15px 30px var(--accent);
}

/* === MOBILE RESPONSIVENESS CONSOLIDATED === */
@media (max-width: 768px) {
  /* Mobile header adjustments */
  header {
    padding: 0;
  }

  nav {
    padding: 0.75rem 1rem;
    justify-content: space-between;
    align-items: center;
  }

  /* General section padding and width */
  section,
  #last-page {
    width: 95%;
    margin: 0 auto;
    padding: 15px 10px;
    box-sizing: border-box;
    text-align: center;
  }

  /* Header and nav */
  .hamburger {
    display: flex;
    background: none;
    border: 2px solid var(--accent);
    color: var(--accent);
    cursor: pointer;
    border-radius: 50%;
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 44px;
    height: 44px;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    z-index: 1001;
  }

  .hamburger:hover,
  .hamburger:focus {
    background-color: var(--accent);
    color: var(--bg-light);
    transform: translateY(-50%) scale(1.05);
    outline: none;
  }

  .hamburger:active {
    transform: translateY(-50%) scale(0.95);
  }

  .theme-toggle {
    right: 1rem !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 44px !important;
    height: 44px !important;
    font-size: 1.2rem !important;
    border: 2px solid var(--accent) !important;
    z-index: 1001 !important;
  }

  .theme-toggle:hover,
  .theme-toggle:focus {
    transform: translateY(-50%) scale(1.05) !important;
  }

  .theme-toggle:active {
    transform: translateY(-50%) scale(0.95) !important;
  }

  .nav-links {
    display: none;
    flex-direction: column;
    position: absolute;
    top: 60px;
    left: 0;
    width: 100%;
    background-color: var(--bg-light);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--accent);
    border-top: none;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out, opacity 0.3s ease;
    opacity: 0;
    z-index: 999;
  }

  .nav-links.active {
    display: flex;
    max-height: 500px;
    opacity: 1;
  }

  .nav-links a {
    display: block !important;
    padding: 1rem 1.5rem !important;
    margin: 0 !important;
    font-size: 1.1rem !important;
    color: var(--text-primary) !important;
    border-bottom: 1px solid var(--accent);
    transition: all 0.3s ease !important;
    text-align: center;
  }

  .nav-links a:last-child {
    border-bottom: none;
  }

  .nav-links a:hover {
    background-color: var(--accent) !important;
    color: var(--bg-light) !important;
    transform: translateX(5px);
  }

  /* Hero adjustments */
  .hero {
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
    height: auto !important;
    padding-left: 20px !important;
    padding-right: 20px !important;
  }

  .hero-content {
    max-width: 100% !important;
    padding: 20px !important;
  }

  .hero-content h1 {
    font-size: 2.2rem !important;
    margin-bottom: 0.6em !important;
  }

  .hero-content p {
    font-size: 1rem !important;
    margin-bottom: 1.2em !important;
  }

  .hero img {
    width: 80% !important;
    max-width: 300px !important;
    height: auto !important;
    margin: 10px auto !important;
    display: block !important;
  }

  /* Social icons reposition & center */
  .social-icons {
    position: static !important;
    margin-top: 15px !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
    gap: 15px !important;
  }

  /* About section stacks vertically */
  #about {
    flex-direction: column !important;
    padding: 0 !important;
  }

  .about-text,
  .about-image {
    flex: 1 1 100% !important;
    max-width: 100% !important;
    padding: 10px 0 !important;
  }

  .about-image img {
    width: 100% !important;
    max-width: 300px !important;
    height: auto !important;
    margin: 10px auto !important;
    display: block;
  }

  /* Skills marquee - make items wrap */
  .skills-marquee {
    padding: 40px 10px !important;
  }

  .marquee-track {
    animation: none !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
  }

  .skill-item {
    min-width: 90px !important;
    margin: 10px !important;
  }

  /* Projects stack vertically */
  #projects .project-list {
    flex-direction: column !important;
    gap: 20px !important;
  }

  #projects .project-item {
    width: 100% !important;
    text-align: left !important;
  }

  #projects .project-item img {
    width: 100% !important;
    height: auto !important;
  }

  /* Timeline list items stack */
  .timeline li {
    flex-direction: column !important;
    align-items: flex-start !important;
    padding-left: 0 !important;
    margin-bottom: 25px !important;
  }

  .timeline li i {
    margin-bottom: 10px !important;
    margin-right: 0 !important;
  }

  /* Testimonials stack vertically */
  .testimonial-list {
    display: flex !important;
    flex-direction: column !important;
    gap: 20px !important;
  }

  /* Contact boxes stack vertically */
  #last-page .contact-container {
    flex-direction: column !important;
    align-items: center !important;
    gap: 20px !important;
  }

  #last-page .contact-box {
    max-width: 100% !important;
    width: 100% !important;
    text-align: center !important;
  }

  /* Buttons adjust */
  .cta-button {
    padding: 10px 20px !important;
    font-size: 1rem !important;
  }
}

/* Call to action buttons in last page */
#last-page .cta-button {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 24px;
  background-color: var(--accent);
  color: var(--bg-light);
  font-weight: 600;
  font-size: 1rem;
  border-radius: 8px;
  text-decoration: none;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  transition: background-color 0.3s ease, transform 0.3s ease,
    box-shadow 0.3s ease;
}

#last-page .cta-button:hover {
  background-color: var(--accent-dark);
  transform: translateY(-3px);
  box-shadow: 0 15px 30px var(--accent);
}

/* Last page headings */
#last-page h2,
#last-page h3 {
  color: var(--text-secondary);
}
